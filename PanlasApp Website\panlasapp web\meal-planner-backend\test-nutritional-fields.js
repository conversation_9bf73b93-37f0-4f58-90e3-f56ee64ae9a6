const axios = require('axios');

// Test script to verify nutritional fields are being saved correctly
const API_BASE_URL = 'http://localhost:5000/api';

// Test data for a meal with all nutritional fields
const testMealData = {
  name: 'Test Meal - Nutritional Fields',
  category: ['Filipino'],
  mealType: ['lunch'],
  dietaryTags: ['Healthy'],
  rating: 4.5,
  prepTime: 30,
  calories: 350,
  protein: 25,
  carbs: 40,
  fat: 12,
  calcium: 150,
  iron: 8.5,
  vitaminA: 200,
  vitaminC: 45,
  phosphorus: 180,
  vitaminB1: 1.2,
  vitaminB2: 1.4,
  vitaminB3: 15,
  price: 150,
  image: 'test-image.jpg',
  description: 'Test meal to verify nutritional fields are saved correctly',
  ingredients: ['Test ingredient 1', 'Test ingredient 2'],
  instructions: ['Test instruction 1', 'Test instruction 2'],
  allergens: ['None'],
  region: 'Philippines',
  servingSize: 4,
  dietType: {
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: false,
    isDairyFree: false,
    isNutFree: true,
    isLowCarb: false,
    isKeto: false,
    isPescatarian: false,
    isHalal: true
  }
};

async function testNutritionalFields() {
  try {
    console.log('🧪 Testing nutritional fields save functionality...\n');

    // You'll need to replace this with a valid admin token
    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************.ObuFeyjysUK9ruxK9dOrZBj_dS9yrTzT-Dlh9hM7ImY';
    
    const config = {
      headers: { 
        'x-auth-token': token,
        'Content-Type': 'application/json'
      }
    };

    // Step 1: Create a new meal
    console.log('📝 Creating test meal with nutritional fields...');
    const createResponse = await axios.post(`${API_BASE_URL}/meals`, testMealData, config);
    
    if (createResponse.status === 201) {
      console.log('✅ Meal created successfully!');
      const createdMeal = createResponse.data;
      console.log(`   Meal ID: ${createdMeal._id}`);
      console.log(`   Meal Name: ${createdMeal.name}`);
      
      // Step 2: Fetch the created meal to verify fields were saved
      console.log('\n🔍 Fetching created meal to verify nutritional fields...');
      const fetchResponse = await axios.get(`${API_BASE_URL}/meals`);
      
      if (fetchResponse.status === 200) {
        const meals = fetchResponse.data;
        const testMeal = meals.find(meal => meal._id === createdMeal._id);
        
        if (testMeal) {
          console.log('✅ Meal found in database!');
          console.log('\n📊 Nutritional Fields Verification:');
          
          // Check each nutritional field
          const fieldsToCheck = [
            'calcium', 'iron', 'vitaminA', 'vitaminC', 
            'phosphorus', 'vitaminB1', 'vitaminB2', 'vitaminB3'
          ];
          
          let allFieldsCorrect = true;
          
          fieldsToCheck.forEach(field => {
            const expected = testMealData[field];
            const actual = testMeal[field];
            const isCorrect = actual === expected;
            
            console.log(`   ${field}: ${actual} (expected: ${expected}) ${isCorrect ? '✅' : '❌'}`);
            
            if (!isCorrect) {
              allFieldsCorrect = false;
            }
          });
          
          // Check diet type
          console.log('\n🥗 Diet Type Verification:');
          if (testMeal.dietType) {
            Object.keys(testMealData.dietType).forEach(key => {
              const expected = testMealData.dietType[key];
              const actual = testMeal.dietType[key];
              const isCorrect = actual === expected;
              
              console.log(`   ${key}: ${actual} (expected: ${expected}) ${isCorrect ? '✅' : '❌'}`);
              
              if (!isCorrect) {
                allFieldsCorrect = false;
              }
            });
          } else {
            console.log('   ❌ Diet type not found in saved meal');
            allFieldsCorrect = false;
          }
          
          // Final result
          console.log(`\n${allFieldsCorrect ? '🎉' : '❌'} Overall Test Result: ${allFieldsCorrect ? 'PASSED' : 'FAILED'}`);
          
          // Step 3: Clean up - delete the test meal
          console.log('\n🧹 Cleaning up test meal...');
          const deleteResponse = await axios.delete(`${API_BASE_URL}/meals/${createdMeal._id}`, config);
          
          if (deleteResponse.status === 200) {
            console.log('✅ Test meal deleted successfully');
          } else {
            console.log('⚠️  Warning: Could not delete test meal');
          }
          
        } else {
          console.log('❌ Test meal not found in database');
        }
      } else {
        console.log('❌ Failed to fetch meals');
      }
      
    } else {
      console.log('❌ Failed to create meal');
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.response?.data || error.message);
  }
}

// Run the test
testNutritionalFields();
