import { StyleSheet } from 'react-native';
import { colors, legacyFonts, legacySpacing, borderRadius, shadows } from './theme';
import { getResponsiveFontSize, getResponsiveSpacing, getResponsiveDimensions, getScreenSize } from '../utils/responsive';

// Function to create responsive styles
export const createResponsiveStyles = (screenSize = null) => {
  const currentScreenSize = screenSize || getScreenSize();
  const dimensions = getResponsiveDimensions(currentScreenSize);

  return StyleSheet.create({
    // Responsive button styles
    button: {
      backgroundColor: colors.primary,
      borderRadius: dimensions.borderRadius,
      paddingVertical: getResponsiveSpacing('md', currentScreenSize),
      paddingHorizontal: getResponsiveSpacing('lg', currentScreenSize),
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: dimensions.buttonHeight,
      minWidth: dimensions.buttonMinWidth,
    },
    buttonText: {
      color: colors.surface,
      fontSize: getResponsiveFontSize('medium', currentScreenSize),
      fontWeight: '600',
    },
    // Responsive header styles
    headerButton: {
      padding: getResponsiveSpacing('sm', currentScreenSize),
      marginLeft: getResponsiveSpacing('sm', currentScreenSize),
      borderRadius: dimensions.borderRadius,
      minWidth: dimensions.buttonHeight,
      minHeight: dimensions.buttonHeight,
      alignItems: 'center',
      justifyContent: 'center',
    },
    // Responsive card styles
    responsiveCard: {
      backgroundColor: colors.surface,
      borderRadius: dimensions.borderRadius,
      padding: dimensions.cardPadding,
      marginBottom: getResponsiveSpacing('md', currentScreenSize),
      ...shadows.small,
    },
  });
};

export const commonStyles = StyleSheet.create({
  // Container styles
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  safeContainer: {
    flex: 1,
    backgroundColor: colors.background,
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: legacySpacing.lg,
  },
  scrollContainer: {
    flexGrow: 1,
    padding: legacySpacing.md,
  },

  // Card styles
  card: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    padding: legacySpacing.md,
    marginBottom: legacySpacing.md,
    ...shadows.small,
  },
  cardLarge: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.large,
    padding: legacySpacing.lg,
    marginBottom: legacySpacing.lg,
    ...shadows.medium,
  },

  // Food/Meal card styles
  foodCard: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    marginBottom: legacySpacing.md,
    overflow: 'hidden',
    flex: 1,
    marginHorizontal: legacySpacing.xs / 2, // Reduced margin for better spacing
    minHeight: 320, // Increased minimum height for consistent layout
    ...shadows.small,
  },
  foodCardImage: {
    position: 'relative',
  },
  foodCardContent: {
    padding: legacySpacing.sm,
    flex: 1, // Allow content to expand and maintain consistent card heights
    flexDirection: 'column', // Ensure vertical layout
    justifyContent: 'space-between', // Push actions to bottom
  },
  foodCardTitle: {
    fontSize: legacyFonts.sizes.small,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: legacySpacing.xs,
    lineHeight: 18, // Better line height for readability
  },
  foodCardMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: legacySpacing.sm,
  },
  categoryTag: {
    backgroundColor: colors.primary,
    paddingHorizontal: legacySpacing.sm,
    paddingVertical: legacySpacing.xs,
    borderRadius: borderRadius.small,
  },
  categoryTagText: {
    color: colors.surface,
    fontSize: legacyFonts.sizes.small,
    fontWeight: '500',
  },
  rating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    color: colors.textSecondary,
    fontSize: legacyFonts.sizes.small,
    marginLeft: legacySpacing.xs,
  },

  // Button styles
  primaryButton: {
    backgroundColor: colors.primary,
    paddingVertical: legacySpacing.md,
    paddingHorizontal: legacySpacing.lg,
    borderRadius: borderRadius.medium,
    alignItems: 'center',
    justifyContent: 'center',
  },
  primaryButtonText: {
    color: colors.surface,
    fontSize: legacyFonts.sizes.medium,
    fontWeight: 'bold',
  },
  secondaryButton: {
    backgroundColor: colors.secondary,
    paddingVertical: legacySpacing.md,
    paddingHorizontal: legacySpacing.lg,
    borderRadius: borderRadius.medium,
    alignItems: 'center',
    justifyContent: 'center',
  },
  secondaryButtonText: {
    color: colors.surface,
    fontSize: legacyFonts.sizes.medium,
    fontWeight: 'bold',
  },
  outlineButton: {
    borderWidth: 1,
    borderColor: colors.primary,
    paddingVertical: legacySpacing.md,
    paddingHorizontal: legacySpacing.lg,
    borderRadius: borderRadius.medium,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  outlineButtonText: {
    color: colors.primary,
    fontSize: legacyFonts.sizes.medium,
    fontWeight: 'bold',
  },

  // Input styles
  inputContainer: {
    marginBottom: legacySpacing.md,
  },
  inputLabel: {
    fontSize: legacyFonts.sizes.medium,
    fontWeight: '500',
    color: colors.text,
    marginBottom: legacySpacing.sm,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.medium,
    paddingVertical: legacySpacing.md,
    paddingHorizontal: legacySpacing.md,
    fontSize: legacyFonts.sizes.medium,
    backgroundColor: colors.surface,
    color: colors.text,
  },
  inputFocused: {
    borderColor: colors.primary,
  },
  inputError: {
    borderColor: colors.error,
  },
  inputErrorText: {
    color: colors.error,
    fontSize: legacyFonts.sizes.small,
    marginTop: legacySpacing.xs,
  },

  // Header styles
  header: {
    backgroundColor: colors.primary,
    paddingVertical: legacySpacing.md,
    paddingHorizontal: legacySpacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: legacyFonts.sizes.large,
    fontWeight: 'bold',
    color: colors.surface,
  },
  headerSubtitle: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.surface,
    opacity: 0.8,
  },

  // Text styles
  title: {
    fontSize: legacyFonts.sizes.xlarge,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: legacySpacing.md,
  },
  subtitle: {
    fontSize: legacyFonts.sizes.large,
    fontWeight: '600',
    color: colors.text,
    marginBottom: legacySpacing.sm,
  },
  bodyText: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.text,
    lineHeight: 22,
  },
  captionText: {
    fontSize: legacyFonts.sizes.small,
    color: colors.textSecondary,
  },

  // List styles
  listItem: {
    backgroundColor: colors.surface,
    paddingVertical: legacySpacing.md,
    paddingHorizontal: legacySpacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  listItemContent: {
    flex: 1,
    marginLeft: legacySpacing.md,
  },
  listItemTitle: {
    fontSize: legacyFonts.sizes.medium,
    fontWeight: '500',
    color: colors.text,
  },
  listItemSubtitle: {
    fontSize: legacyFonts.sizes.small,
    color: colors.textSecondary,
    marginTop: legacySpacing.xs,
  },

  // Loading styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  loadingText: {
    marginTop: legacySpacing.md,
    fontSize: legacyFonts.sizes.medium,
    color: colors.textSecondary,
  },

  // Empty state styles
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: legacySpacing.xxl,
    paddingHorizontal: legacySpacing.lg,
  },
  emptyTitle: {
    fontSize: legacyFonts.sizes.large,
    fontWeight: 'bold',
    color: colors.textSecondary,
    marginTop: legacySpacing.md,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.textSecondary,
    marginTop: legacySpacing.sm,
    textAlign: 'center',
    lineHeight: 22,
  },

  // Utility styles
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  spaceAround: {
    justifyContent: 'space-around',
  },
  spaceEvenly: {
    justifyContent: 'space-evenly',
  },
  flex1: {
    flex: 1,
  },
  textCenter: {
    textAlign: 'center',
  },
  textRight: {
    textAlign: 'right',
  },
  marginTop: {
    marginTop: legacySpacing.md,
  },
  marginBottom: {
    marginBottom: legacySpacing.md,
  },
  paddingHorizontal: {
    paddingHorizontal: legacySpacing.md,
  },
  paddingVertical: {
    paddingVertical: legacySpacing.md,
  },

  // Shadow styles
  shadowSmall: shadows.small,
  shadowMedium: shadows.medium,
  shadowLarge: shadows.large,
});
